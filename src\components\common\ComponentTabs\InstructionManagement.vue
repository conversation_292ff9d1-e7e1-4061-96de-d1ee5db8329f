<script setup lang="tsx" generic="T extends 'account' | 'product'">
import { computed, onMounted, shallowRef, useTemplateRef, watch } from 'vue';
import { ElMessage, TableV2SortOrder } from 'element-plus';
import VirtualizedTable from '../VirtualizedTable.vue';
import TodayOrders from './TodayOrders.vue';
import type { AccountInfo, ColumnDefinition, RowAction, ProductInfo } from '@/types';
import { formatDateTime } from '@/script/formatter';
import { RecordService } from '@/api';
import type { InstructionInfo } from '../../../../../xtrade-sdk/dist';

const { type, activeItem } = defineProps<{
  type: T;
  activeItem?: T extends 'account' ? AccountInfo : ProductInfo;
}>();

// 指令状态选项
const instructionStatusOptions = [
  { label: '待审批', value: 1 },
  { label: '执行中', value: 0 }, // 根据系统枚举，0表示通过/执行中
  { label: '审批中', value: 3 }, // 新增审批中状态
  { label: '已完成', value: 4 }, // 新增已完成状态
  { label: '已拒绝/已撤销', value: 2 }, // 2表示驳回
];

// 当前选中的指令状态
const selectedInstructionStatus = shallowRef(1); // 默认选中待审批

// 指令列表
const instructions = shallowRef<InstructionInfo[]>([]);

// 选中的指令
const selectedInstruction = shallowRef<InstructionInfo>();

// 是否显示订单详情
const showOrderDetails = shallowRef(false);

// 表格引用
const tableRef = useTemplateRef('tableRef');

// 指令列表列定义
const instructionColumns = [
  {
    key: 'workFlowId',
    title: '编号',
    width: 120,
    sortable: true,
  },
  {
    key: 'sourceUserName',
    title: '发起人',
    width: 100,
    sortable: true,
  },
  {
    key: 'instructionType',
    title: '类型',
    width: 100,
    sortable: true,
    cellRenderer: (params: any) => {
      const typeMap = {
        0: '普通交易',
        1: '算法交易',
      };
      return typeMap[params.cellData as keyof typeof typeMap] || '未知';
    },
  },
  {
    key: 'instructionStatus',
    title: '状态',
    width: 100,
    sortable: true,
    cellRenderer: (params: any) => {
      const statusMap = {
        0: { label: '已通过', color: 'text-green-500' },
        1: { label: '待审批', color: 'text-orange-500' },
        2: { label: '已驳回', color: 'text-red-500' },
        3: { label: '审批中', color: 'text-blue-500' },
        4: { label: '已完成', color: 'text-gray-500' },
      };
      const status = statusMap[params.cellData as keyof typeof statusMap];
      return <span class={status?.color}>{status?.label || '未知'}</span>;
    },
  },
  {
    key: 'workFlowName',
    title: '流程',
    width: 120,
    sortable: true,
  },
  {
    key: 'currentNode',
    title: '当前节点',
    width: 100,
    sortable: true,
    cellRenderer: () => '--', // 暂时显示占位符
  },
  {
    key: 'executeStatus',
    title: '执行进度',
    width: 100,
    sortable: true,
    cellRenderer: (params: any) => {
      return params.cellData ? '已结束' : '进行中';
    },
  },
  {
    key: 'instrumentName',
    title: '合约',
    width: 120,
    sortable: true,
  },
  {
    key: 'direction',
    title: '方向',
    width: 80,
    sortable: true,
    cellRenderer: (params: any) => {
      return (
        <span class={params.cellData === 1 ? 'text-red-500' : 'text-green-500'}>
          {params.cellData === 1 ? '买入' : '卖出'}
        </span>
      );
    },
  },
  {
    key: 'volumeOriginal',
    title: '指令数量',
    width: 100,
    align: 'right',
    sortable: true,
  },
  {
    key: 'instructionPrice',
    title: '指令价格',
    width: 100,
    align: 'right',
    sortable: true,
    cellRenderer: () => '--', // 暂时显示占位符，因为InstructionInfo接口中没有价格字段
  },
  {
    key: 'priceType',
    title: '价格类型',
    width: 100,
    sortable: true,
    cellRenderer: () => '--', // 暂时显示占位符
  },
  {
    key: 'startTime',
    title: '开始时间',
    width: 160,
    sortable: true,
    cellRenderer: (params: any) => formatDateTime(params.cellData, 'MM-dd hh:mm:ss'),
  },
  {
    key: 'tradedVolume',
    title: '成交数量',
    width: 100,
    align: 'right',
    sortable: true,
    cellRenderer: () => '--', // 暂时显示占位符
  },
  {
    key: 'sourceIP',
    title: '发起人IP',
    width: 120,
    sortable: true,
    cellRenderer: () => '--', // 暂时显示占位符
  },
  {
    key: 'sourceMAC',
    title: '发起人MAC',
    width: 140,
    sortable: true,
    cellRenderer: () => '--', // 暂时显示占位符
  },
] as ColumnDefinition<InstructionInfo>;

// 行操作定义
const rowActions: RowAction<InstructionInfo>[] = [
  {
    label: '通过',
    show: (row: InstructionInfo) => row.instructionStatus === 1 || row.instructionStatus === 3, // 待审批或审批中
    onClick: (row: InstructionInfo) => {
      handleApproveInstruction(row.workFlowId);
    },
    type: 'var(--g-green)',
  },
  {
    label: '拒绝',
    show: (row: InstructionInfo) => row.instructionStatus === 1 || row.instructionStatus === 3, // 待审批或审批中
    onClick: (row: InstructionInfo) => {
      handleRejectInstruction(row.workFlowId);
    },
    type: 'var(--g-red)',
  },
  {
    label: '撤销',
    show: (row: InstructionInfo) => row.instructionStatus === 0, // 执行中
    onClick: (row: InstructionInfo) => {
      handleCancelInstruction(row.workFlowId);
    },
    type: 'var(--g-red)',
  },
];

// 过滤指令数据
const filteredInstructions = computed(() => {
  return instructions.value.filter(instruction => {
    if (selectedInstructionStatus.value === 0) return true; // 全部
    return instruction.instructionStatus === selectedInstructionStatus.value;
  });
});

// 加载指令列表
const loadInstructions = async () => {
  try {
    const data = await RecordService.getTodayInstructions();
    instructions.value = data;
    // 如果有数据，默认选中第一行
    if (data.length > 0) {
      selectedInstruction.value = data[0];
    }
  } catch (error) {
    console.error('加载指令列表失败:', error);
    ElMessage.error('加载指令列表失败');
    instructions.value = [];
  }
};

// 处理指令行点击
const handleInstructionRowClick = (instruction: InstructionInfo) => {
  selectedInstruction.value = instruction;
};

// 批量操作函数
const handleBatchApprove = () => {
  const selectedRows = tableRef.value?.selectedRows ?? [];
  if (selectedRows.length === 0) {
    ElMessage.warning('请选择要通过的指令');
    return;
  }

  const approveableRows = selectedRows.filter(
    row => row.instructionStatus === 1 || row.instructionStatus === 3,
  );

  if (approveableRows.length === 0) {
    ElMessage.warning('所选指令中没有可通过的指令');
    return;
  }

  approveableRows.forEach(row => {
    handleApproveInstruction(row.workFlowId);
  });
};

const handleBatchReject = () => {
  const selectedRows = tableRef.value?.selectedRows ?? [];
  if (selectedRows.length === 0) {
    ElMessage.warning('请选择要拒绝的指令');
    return;
  }

  const rejectableRows = selectedRows.filter(
    row => row.instructionStatus === 1 || row.instructionStatus === 3,
  );

  if (rejectableRows.length === 0) {
    ElMessage.warning('所选指令中没有可拒绝的指令');
    return;
  }

  rejectableRows.forEach(row => {
    handleRejectInstruction(row.workFlowId);
  });
};

const handleBatchCancel = () => {
  const selectedRows = tableRef.value?.selectedRows ?? [];
  if (selectedRows.length === 0) {
    ElMessage.warning('请选择要撤销的指令');
    return;
  }

  const cancelableRows = selectedRows.filter(row => row.instructionStatus === 0);

  if (cancelableRows.length === 0) {
    ElMessage.warning('所选指令中没有可撤销的指令');
    return;
  }

  cancelableRows.forEach(row => {
    handleCancelInstruction(row.workFlowId);
  });
};

// 单个指令操作函数
const handleApproveInstruction = (workFlowId: string) => {
  // TODO: 调用审批通过接口
  ElMessage.success(`指令 ${workFlowId} 审批通过`);
  loadInstructions(); // 重新加载数据
};

const handleRejectInstruction = (workFlowId: string) => {
  // TODO: 调用审批拒绝接口
  ElMessage.success(`指令 ${workFlowId} 审批拒绝`);
  loadInstructions(); // 重新加载数据
};

const handleCancelInstruction = (workFlowId: string) => {
  // TODO: 调用撤销指令接口
  ElMessage.success(`指令 ${workFlowId} 已撤销`);
  loadInstructions(); // 重新加载数据
};

// 切换订单详情显示
const toggleOrderDetails = () => {
  showOrderDetails.value = !showOrderDetails.value;
};

// 监听activeItem变化
watch(
  () => activeItem,
  newItem => {
    if (newItem) {
      loadInstructions();
    }
  },
  { deep: true },
);

onMounted(() => {
  loadInstructions();
});
</script>

<template>
  <div class="instruction-management" h-full flex="~ col">
    <!-- 指令列表 -->
    <div class="instruction-list" flex-1>
      <VirtualizedTable
        ref="tableRef"
        :columns="instructionColumns"
        :data="filteredInstructions"
        :sort="{ key: 'startTime', order: TableV2SortOrder.DESC }"
        :row-actions="rowActions"
        :row-height="36"
        :header-row-height="36"
        select
        :showTotal="false"
        fixed
        @row-click="handleInstructionRowClick"
      >
        <template #left>
          <div flex aic>
            <span mr-8>指令状态:</span>
            <div w-120 mr-16>
              <el-select v-model="selectedInstructionStatus" placeholder="选择状态" size="small">
                <el-option
                  v-for="status in instructionStatusOptions"
                  :key="status.value"
                  :label="status.label"
                  :value="status.value"
                />
              </el-select>
            </div>
          </div>
        </template>
        <template #actions>
          <div class="actions" flex aic>
            <el-button @click="handleBatchApprove" size="small" color="var(--g-block-bg-1)">
              批量通过
            </el-button>
            <el-button @click="handleBatchReject" size="small" color="vvar(--g-block-bg-1)">
              批量拒绝
            </el-button>
            <el-button @click="handleBatchCancel" size="small" color="var(--g-block-bg-1)">
              批量撤销
            </el-button>
            <el-button @click="toggleOrderDetails" size="small" color="var(--g-block-bg-1)">
              {{ showOrderDetails ? '隐藏' : '显示' }}指令详情
            </el-button>
          </div>
        </template>
      </VirtualizedTable>
    </div>

    <!-- 订单详情 -->
    <div
      v-if="showOrderDetails && selectedInstruction"
      class="order-details"
      h-300
      border-t="1 solid var(--g-border-color)"
    >
      <div class="order-header" h-40 flex aic px-16 bg="[--g-bg]">
        <span class="title" fs-14 fw-600>指令订单详情 - {{ selectedInstruction.workFlowId }}</span>
      </div>
      <div class="order-content" flex-1 min-h-1>
        <TodayOrders type="instruction" :activeItem="selectedInstruction" :hasAction="false" />
      </div>
    </div>
  </div>
</template>

<style scoped></style>
