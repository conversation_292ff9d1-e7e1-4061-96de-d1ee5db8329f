import { AssetTypeEnum, TradeDirectionEnum } from '@/enum';
import type { BasketOrderPreview } from '@/types/basket';

export const MockedDataList: BasketOrderPreview[] = [
  {
    id: 1,
    instrumentName: '比亚迪',
    instrument: 'SZSE.002594',
    assetType: AssetTypeEnum.股票,
    direction: TradeDirectionEnum.买入,
    volume: 1000,
    price: 3200.5,
    amount: 3200500,
    ceilingPrice: 3300.0,
    floorPrice: 3100.0,
    yesterdayPosition: 500,
    todayPosition: 300,
    closablePosition: 200,
  },
  {
    id: 2,
    instrumentName: '万科A',
    instrument: 'SZSE.000002',
    assetType: AssetTypeEnum.股票,
    direction: TradeDirectionEnum.卖出,
    volume: 500,
    price: 4100.2,
    amount: 2050100,
    ceilingPrice: 4200.0,
    floorPrice: 4000.0,
    yesterdayPosition: 800,
    todayPosition: 200,
    closablePosition: 150,
  },
  {
    id: 3,
    instrumentName: '招商银行',
    instrument: 'SHSE.600036',
    assetType: AssetTypeEnum.股票,
    direction: TradeDirectionEnum.买入,
    volume: 750,
    price: 6200.8,
    amount: 4650600,
    ceilingPrice: 6300.0,
    floorPrice: 6100.0,
    yesterdayPosition: 0,
    todayPosition: 750,
    closablePosition: 0,
  },
  {
    id: 4,
    instrumentName: '东方财富',
    instrument: 'SZSE.300059',
    assetType: AssetTypeEnum.股票,
    direction: TradeDirectionEnum.买入,
    volume: 300,
    price: 2100.3,
    amount: 630090,
    ceilingPrice: 2200.0,
    floorPrice: 2000.0,
    yesterdayPosition: 100,
    todayPosition: 200,
    closablePosition: 50,
  },
  {
    id: 5,
    instrumentName: '贵州茅台',
    instrument: 'SHSE.600519',
    assetType: AssetTypeEnum.股票,
    direction: TradeDirectionEnum.卖出,
    volume: 100,
    price: 1750.0,
    amount: 175000,
    ceilingPrice: 1800.0,
    floorPrice: 1700.0,
    yesterdayPosition: 200,
    todayPosition: 50,
    closablePosition: 150,
  },
];
