/**
 * 算法参数项
 */
export interface AlgoParam {
  /**
   * 参数标签/显示名称
   */
  label: string;

  /**
   * 属性名称
   */
  prop: string;

  /**
   * 参数类型标识符
   */
  type: number;

  /**
   * 参数描述/备注
   */
  remark: string;

  /**
   * 指示参数是否为必填项
   */
  required: boolean;

  /**
   * 参数默认值
   */
  defaultValue: string;

  /**
   * 用户选项
   */
  uoptions: any[];

  /**
   * 指示参数是否应显示
   */
  display: boolean;

  /**
   * 指示参数是否用于添加功能
   */
  adding: boolean;

  /**
   * 选项标签
   */
  optionLabel: string;

  /**
   * 选项值
   */
  optionValue: string;
}

/**
 * 策略交易算法信息
 */
export interface TradingAlgorithm {
  /**
   * 算法ID（如果未分配则为null）
   */
  id: number;

  /**
   * 算法外部ID
   */
  externalId: number | string;

  /**
   * 算法名称
   */
  name: string;

  /**
   * 供应商标识符（算法提供商）
   */
  vendorId: number | string;

  /**
   * 券商标识符（通常为券商名称首字母）（适用于哪个券商）
   */
  brokerId: string;

  /**
   * 算法备注或描述
   */
  remark: string | null;

  /**
   * 算法参数数组
   */
  params: AlgoParam[];

  /**
   * 策略类型标识符
   */
  strategyType: number;

  /**
   * 创建用户ID
   */
  userId: number;
}
