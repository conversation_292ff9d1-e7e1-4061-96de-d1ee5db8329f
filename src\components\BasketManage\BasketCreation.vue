<script setup lang="tsx">
import { ref } from 'vue';
import { ElSelect, ElOption, ElInputNumber } from 'element-plus';
import VirtualizedTable from '@/components/common/VirtualizedTable.vue';
import type { ColumnDefinition, RowAction } from '@/types';
import type { BasketInfoItem } from '../../../../xtrade-sdk/dist';
import { remove } from '@/script';

import {
  AssetTypeEnum,
  ASSET_TYPES,
  TradeDirectionEnum,
  ABSKET_TRADE_DIRECTIONS,
} from '@/enum/trade';

const records = ref<BasketInfoItem[]>([
  {
    instrumentName: '平安银行',
    instrument: 'SHSE.000001',
    assetType: AssetTypeEnum.股票,
    direction: TradeDirectionEnum.买入,
    volume: 1000,
    weight: 20,
  },
  {
    instrumentName: '沪深300',
    instrument: 'SHSE.600282',
    assetType: AssetTypeEnum.股票,
    direction: TradeDirectionEnum.卖出,
    volume: 500,
    weight: 30,
  },
]);

// Editable cell components
const EditableInputNumber = (props: {
  modelValue: string | number;
  precision: number;
  step: number;
  onUpdateModelValue: (value: string | number) => void;
}) => {
  return (
    <ElInputNumber
      modelValue={props.modelValue}
      onChange={props.onUpdateModelValue as any}
      controls={false}
      precision={props.precision}
      step={props.step}
      size="small"
      style="width: 100%"
    />
  );
};

const EditableSelect = (props: {
  modelValue: number;
  onUpdateModelValue: (value: number) => void;
  options: { label: string; value: number }[];
  disabled?: boolean;
}) => {
  return (
    <ElSelect
      modelValue={props.modelValue}
      onChange={props.onUpdateModelValue}
      disabled={props.disabled}
      size="small"
      style="width: 100%"
    >
      {props.options.map(option => (
        <ElOption key={option.value} label={option.label} value={option.value} />
      ))}
    </ElSelect>
  );
};

const updateAssetType = (row: BasketInfoItem, value: number) => {
  row.assetType = value;
};

const updateDirection = (row: BasketInfoItem, value: number) => {
  row.direction = value;
};

const updateVolume = (row: BasketInfoItem, value: number) => {
  row.volume = value;
};

const updateWeight = (row: BasketInfoItem, value: number) => {
  row.weight = value;
};

const getVolumeStep = (assetType: number) => {
  switch (assetType) {
    case AssetTypeEnum.股票:
      return 100;
    default:
      return 1;
  }
};

// Column definitions with editable cells
const columns: ColumnDefinition<BasketInfoItem> = [
  {
    key: 'instrumentName',
    title: '合约名称',
    width: 150,
    cellRenderer: ({ rowData }) => <span>{rowData.instrumentName}</span>,
  },
  {
    key: 'instrument',
    title: '合约代码',
    width: 120,
    cellRenderer: ({ rowData }) => <span>{rowData.instrument}</span>,
  },
  {
    key: 'assetType',
    title: '资产类型',
    width: 120,
    cellRenderer: ({ rowData }) => (
      <EditableSelect
        disabled={true}
        modelValue={rowData.assetType}
        onUpdateModelValue={value => updateAssetType(rowData, value)}
        options={ASSET_TYPES}
      />
    ),
  },
  {
    key: 'direction',
    title: '买卖方向',
    width: 100,
    cellRenderer: ({ rowData }) => (
      <EditableSelect
        modelValue={rowData.direction}
        onUpdateModelValue={value => updateDirection(rowData, value)}
        options={ABSKET_TRADE_DIRECTIONS}
      />
    ),
  },
  {
    key: 'volume',
    title: '委托数量',
    width: 120,
    cellRenderer: ({ rowData }) => (
      <EditableInputNumber
        precision={0}
        step={getVolumeStep(rowData.assetType)}
        modelValue={rowData.volume}
        onUpdateModelValue={value => updateVolume(rowData, value as number)}
      />
    ),
  },
  {
    key: 'weight',
    title: '权重',
    width: 120,
    cellRenderer: ({ rowData }) => (
      <EditableInputNumber
        precision={2}
        step={0.01}
        modelValue={rowData.weight}
        onUpdateModelValue={value => updateWeight(rowData, value as number)}
      />
    ),
  },
];

// Row actions
const rowActions: RowAction<BasketInfoItem>[] = [
  {
    label: '删除',
    icon: 'remove',
    type: 'text',
    onClick: row => {
      remove(records.value, x => x.instrument == row.instrument);
    },
  },
];
</script>

<template>
  <div class="basket-detail-list" h-300>
    <VirtualizedTable
      :data="records"
      :columns="columns"
      :row-actions="rowActions"
      :row-action-width="80"
      fixed
    />
  </div>
  <div class="basket-toolbar" flex aic jcc>
    <div class="toolbar" h-48 lh-48>
      <el-button style="width: 240px">取消</el-button>
      <el-button type="primary" style="width: 240px">导入</el-button>
    </div>
  </div>
</template>

<style scoped></style>
