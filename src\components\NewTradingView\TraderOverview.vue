<script setup lang="ts">
import { computed } from 'vue';
import ComponentTabs from '@/components/common/ComponentTabs.vue';
import type { ComponentTab, AccountInfo } from '@/types';

const { selectedAccount } = defineProps<{
  selectedAccount?: AccountInfo;
}>();

const emit = defineEmits<{
  accountSelect: [account: AccountInfo];
}>();

// 定义tabs配置
const tabs = computed<ComponentTab[]>(() => [
  {
    label: '我的账号',
    component: 'AccountList',
    props: {
      trade: true,
    },
    events: {
      'row-click': (account: AccountInfo) => emit('accountSelect', account),
    },
  },
  {
    label: '我的指令',
    component: 'InstructionManagement',
    props: {
      activeItem: selectedAccount,
      type: 'account',
    },
  },
  {
    label: '我的委托',
    component: 'TodayOrders',
    props: {
      activeItem: selectedAccount,
      type: 'account',
    },
  },
  {
    label: '我的持仓',
    component: 'TodayPositions',
    props: {
      activeItem: selectedAccount,
      type: 'account',
    },
  },
  {
    label: '我的成交',
    component: 'TodayRecords',
    props: {
      activeItem: selectedAccount,
      type: 'account',
    },
  },
  {
    label: '我的产品',
    component: 'ProductList',
    props: {
      trade: true,
    },
  },
]);
</script>

<template>
  <div h-full flex="~ col">
    <ComponentTabs h-full :tabs="tabs" />
  </div>
</template>

<style scoped></style>
