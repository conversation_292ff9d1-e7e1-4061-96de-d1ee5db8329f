<script setup lang="tsx" generic="T extends AnyObject">
import type { AnyObject, ColumnDefinition, RowAction } from '@/types';
import type { FunctionalComponent, Ref } from 'vue';
import type { CheckboxValueType, RowEventHandlers } from 'element-plus';
import { ElCheckbox, TableV2FixedDir, TableV2SortOrder, type SortBy } from 'element-plus';
import { computed, shallowRef, useTemplateRef, onMounted, ref } from 'vue';
import { useFilteredData } from '@/composables/useFilteredData';
import { Search, Loading } from '@element-plus/icons-vue';
import TableRowButton from './TableRowButton.vue';
import { PAGE_SIZE } from '@/enum';

// 异步数据加载器类型
export type AsyncDataLoader<T> = (pageNo: number) => Promise<{
  data: T[];
  total: number;
}>;

const {
  columns,
  data,
  select,
  identity = 'id',
  sort = { key: '', order: TableV2SortOrder.DESC },
  rowActions = [],
  rowActionWidth = 80,
  headerRowHeight = 44,
  rowHeight = 44,
  fixed = false,
  showIndex = false,
  searchPlaceholder = '搜索',
  showToolbar = true,
  enableSearch = true,
  alterStripe = false,
  showTotal = true,
  customFilter,
  asyncDataLoader,
  pageSize = PAGE_SIZE,
} = defineProps<{
  columns: ColumnDefinition<T>;
  data?: T[];
  /** 是否显示选择列 */
  select?: boolean;
  /** 行数据的唯一标识字段 */
  identity?: keyof T;
  /** 默认排序列 */
  sort?: SortBy;
  /** 行操作 */
  rowActions?: RowAction<T>[];
  /** 行操作列宽度 */
  rowActionWidth?: number;
  /** 标题行高 */
  headerRowHeight?: number;
  /** 行高 */
  rowHeight?: number;
  /** 单元格宽度是自适应还是固定 */
  fixed?: boolean;
  /** 是否显示序号列 */
  showIndex?: boolean;
  /** 搜索框占位符 */
  searchPlaceholder?: string;
  /** 自定义过滤 */
  customFilter?: (item: T) => boolean;
  /** 是否显示toolbar */
  showToolbar?: boolean;
  /** 是否显示搜索框 - 需要在显示搜索框的情况下生效 */
  enableSearch?: boolean;
  /** 替代隔行变色 */
  alterStripe?: boolean;
  /** 异步数据加载器 */
  asyncDataLoader?: AsyncDataLoader<T>;
  /** 分页大小 */
  pageSize?: number;
  /** 是否显示总数 */
  showTotal?: boolean;
}>();

type SelectionCellProps = {
  value: boolean;
  intermediate?: boolean;
  onChange: (value: CheckboxValueType) => void;
};

const emit = defineEmits<{
  'row-dblclick': [rowData: T];
  'row-click': [rowData: T];
}>();

const rowEventHandlers: RowEventHandlers = {
  onClick: ({ rowData }) => {
    clickedRow.value = rowData;
    emit('row-click', rowData);
  },
  onDblclick: ({ rowData }) => {
    emit('row-dblclick', rowData);
  },
};

// 选择列
const SelectionCell: FunctionalComponent<SelectionCellProps> = ({
  value,
  intermediate = false,
  onChange,
}) => {
  return <ElCheckbox onChange={onChange} modelValue={value} indeterminate={intermediate} />;
};

// 默认排序状态
const sortState = shallowRef<SortBy>({
  key: sort.key,
  order: sort.order,
});

// 单击选中的行
const clickedRow = shallowRef<T | null>(null);

// 异步模式下的数据管理
const asyncData = ref<T[]>([]) as Ref<T[]>;
const isLoading = ref(false);
const asyncTotalCount = ref(0);
// 记录已加载的分页信息，防止重复加载
const loadedPages = ref(new Set<number>());
const loadingPages = ref(new Set<number>());

// 获取实际使用的数据源
const getDataSource = () => {
  if (asyncDataLoader) {
    return asyncData.value;
  }
  return data || [];
};

// 过滤，增加选择列，排序，最终获得的列表数组
const { filteredData, query, selectedRowsMap, tableKey } = useFilteredData(getDataSource, {
  customFilter,
  identity,
  select,
  sortState,
  columns,
  fixed,
});

const tablewrapper = useTemplateRef('tablewrapper');

// 已选择的行
const selectedRows = computed<T[]>(() => {
  if (!select) return [];
  const dataSource = getDataSource();
  return dataSource.filter(row => {
    const key = row[identity] as string | number;
    return selectedRowsMap.value[key];
  });
});

// 总记录数
const totalRecords = computed(() => {
  return filteredData.value.length;
});

// 异步加载数据的函数
const loadDataRange = async (pageNo: number) => {
  if (!asyncDataLoader) return;
  if (isLoading.value) return;
  // 标记该页正在加载，防止重复请求
  loadingPages.value.add(pageNo);

  try {
    isLoading.value = true;
    const result = await asyncDataLoader(pageNo);
    // 更新数据
    result.data.forEach((item, index) => {
      asyncData.value[(pageNo - 1) * pageSize + index] = item;
    });
    asyncTotalCount.value = result.total;
    loadedPages.value.add(pageNo);
  } catch (error) {
    console.error('Failed to load data:', error);
  } finally {
    isLoading.value = false;
  }
};

// 组件挂载时初始化异步数据
onMounted(() => {
  if (asyncDataLoader) {
    // 加载第一页数据
    loadDataRange(1);
  }
});

// 添加选择列和操作列
const tableColumns = computed(() => {
  const fullColumns: ColumnDefinition<T> = [...columns];

  // 字段补齐
  fullColumns.forEach(col => {
    if (!col.dataKey) {
      col.dataKey = col.key;
    }
  });

  if (select) {
    const selectionColumn: ColumnDefinition<T>[0] = {
      key: 'selection',
      title: '',
      align: 'center',
      width: 50,
      fixed: TableV2FixedDir.LEFT,
      cellRenderer: ({ rowData }: { rowData: T }) => {
        return (
          <SelectionCell
            value={selectedRowsMap.value[rowData[identity]]}
            onChange={val => {
              const key = rowData[identity] as string | number;
              selectedRowsMap.value[key] = val as boolean;
            }}
          />
        );
      },
      headerCellRenderer: () => {
        const onChange = (value: CheckboxValueType) => {
          filteredData.value.forEach(row => {
            const key = row[identity] as string | number;
            selectedRowsMap.value[key] = value as boolean;
          });
        };
        const allSelected = filteredData.value.every(row => row.checked);
        const containsChecked = filteredData.value.some(row => row.checked);
        return (
          <SelectionCell
            value={allSelected}
            intermediate={containsChecked && !allSelected}
            onChange={onChange}
          />
        );
      },
    };
    fullColumns.unshift(selectionColumn);
  }

  if (showIndex) {
    const indexColumn: ColumnDefinition<T>[0] = {
      key: 'index',
      title: 'NO.',
      fixed: TableV2FixedDir.LEFT,
      width: 30,
      align: 'center',
      cellRenderer: ({ rowIndex }) => <span>{rowIndex + 1}</span>,
    };
    fullColumns.unshift(indexColumn);
  }

  if (rowActions.length) {
    const actionsColumn: ColumnDefinition<T>[0] = {
      key: 'actions',
      title: '操作',
      fixed: TableV2FixedDir.RIGHT,
      width: rowActionWidth,
      cellRenderer: ({ rowData }: { rowData: T }) => {
        return (
          <div class="flex aic">
            {rowActions.map(action => {
              if (action.show && !action.show(rowData)) {
                return null;
              } else {
                return <TableRowButton action={action} rowData={rowData} />;
              }
            })}
          </div>
        );
      },
    };
    fullColumns.push(actionsColumn);
  }

  return fullColumns;
});

const getRowClass = ({ rowIndex }: { rowIndex: number }) => {
  if (clickedRow.value) {
    const key = clickedRow.value[identity] as string | number;
    if (filteredData.value[rowIndex][identity] === key) {
      return 'important-bg-[var(--g-active-row)]';
    }
  }
  if (alterStripe) {
    return rowIndex % 2 === 0
      ? 'bg-[var(--g-table-even-row-bg)]'
      : 'bg-[var(--g-table-odd-row-bg)]';
  } else {
    return rowIndex % 2 === 0
      ? 'bg-[var(--g-table-odd-row-bg)]'
      : 'bg-[var(--g-table-even-row-bg)]';
  }
};

const onSort = (sortBy: SortBy) => {
  sortState.value = sortBy;
};

/** 外部主动点击行 */
const clickRow = (rowData: T) => {
  rowEventHandlers.onClick!({ rowData } as any);
};

const selectRows = function (keys: string[] | number[]) {
  for (const key in selectedRowsMap.value) {
    selectedRowsMap.value[key] = false;
  }

  keys.forEach(key => {
    selectedRowsMap.value[key] = true;
  });
};

// 刷新异步数据
const refreshAsyncData = () => {
  if (asyncDataLoader) {
    asyncData.value = [];
    loadedPages.value.clear();
    loadingPages.value.clear();
    asyncTotalCount.value = 0;
    isLoading.value = false;
    loadDataRange(1);
  }
};

// 预加载指定范围的数据
const preloadData = (pageNos: number) => {
  if (asyncDataLoader) {
    for (let i = 1; i <= pageNos; i++) {
      loadDataRange(i);
    }
  }
};

const handleEndReached = () => {
  if (asyncDataLoader && loadedPages.value.size < Math.ceil(asyncTotalCount.value / pageSize)) {
    const lastLoadedPage = Math.max(...Array.from(loadedPages.value), 0);
    loadDataRange(lastLoadedPage + 1);
  }
};

defineExpose({
  selectRows,
  selectedRows,
  clickRow,
  refreshAsyncData,
  preloadData,
  isLoading,
});
</script>
<template>
  <div ref="tablewrapper" flex="~ col" w-full h-full>
    <div v-if="showToolbar" class="toolbar" h-57 px-15 py-8 flex aic jcsb bg="[--g-bg]">
      <div flex aic>
        <el-input
          v-if="enableSearch"
          :prefix-icon="Search"
          class="typical-search-box w-270! mr-10"
          :placeholder="searchPlaceholder"
          v-model="query"
          clearable
        ></el-input>
        <slot name="left"></slot>
        <span v-if="showTotal" fs-14 fw-400 class="c-[var(--g-text-color-1)]">
          共{{ totalRecords }}条数据
        </span>
      </div>
      <slot name="actions"></slot>
    </div>
    <ElAutoResizer ref="resizer" flex-1 min-h-1>
      <template #default="{ height, width }">
        <ElTableV2
          ref="eltable2"
          :key="tableKey"
          :columns="tableColumns"
          :data="filteredData"
          :width="width"
          :height="height"
          :header-row-height="headerRowHeight"
          :row-height="rowHeight"
          :row-class="getRowClass"
          :row-event-handlers="rowEventHandlers"
          :sort-by="sortState"
          :fixed
          v-bind="$attrs"
          @column-sort="onSort"
          @end-reached="handleEndReached"
        ></ElTableV2>
      </template>
    </ElAutoResizer>
  </div>
</template>
<style scoped>
.toolbar :deep(.el-button) {
  height: 36px;
  border-radius: 100px;
  --el-button-font-weight: 400;
}
</style>
