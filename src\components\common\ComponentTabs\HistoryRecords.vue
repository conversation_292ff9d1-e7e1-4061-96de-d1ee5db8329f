<script setup lang="tsx" generic="T extends 'account' | 'product'">
import { computed, onMounted, shallowRef, useTemplateRef, watch } from 'vue';
import { TableV2SortOrder } from 'element-plus';
import VirtualizedTable from '../VirtualizedTable.vue';
import type { AccountInfo, ColumnDefinition } from '@/types';
import {
  exchangeOrderIdCol,
  userNameCol,
  instrumentCol,
  instrumentNameCol,
  directionCol,
  volumeCol,
  tradedPriceCol,
  commissionCol,
  tradeIdCol,
  tradeTimeCol,
  assetTypeCol,
  accountNameCol,
} from './shared/columnDefinitions';
import DateRangePicker from './shared/DateRangePicker.vue';
import { getDefaultDateRange } from '@/script';
import { PAGE_SIZE } from '@/enum';

import {
  type HistoryQueryOptions,
  type LegacyFundInfo,
  type TradeRecordInfo,
  IdentityType,
  Repos,
} from '../../../../../xtrade-sdk/dist';

const recordsRepo = new Repos.RecordsRepo();

// 定义组件属性
const { type, activeItem } = defineProps<{
  type: T;
  activeItem?: T extends 'account' ? AccountInfo : LegacyFundInfo;
}>();

// 基础列定义
const baseColumns = [
  exchangeOrderIdCol,
  userNameCol,
  instrumentCol,
  instrumentNameCol,
  directionCol,
  volumeCol,
  tradedPriceCol,
  commissionCol,
  tradeIdCol,
  tradeTimeCol,
  assetTypeCol,
] as ColumnDefinition<TradeRecordInfo>;

// 根据类型动态生成列
const columns = computed(() => {
  const cols = [...baseColumns];
  if (type === 'product') {
    cols.splice(1, 0, accountNameCol as any);
  }
  return cols;
});

// 历史成交数据
const historyRecords = shallowRef<TradeRecordInfo[]>([]);
const tableRef = useTemplateRef('tableRef');

// 日期范围
const dateRange = shallowRef<[string, string]>(getDefaultDateRange());

// 获取历史成交数据
const fetchHistoryRecords = async () => {
  if (!activeItem) return;

  // 构建查询参数
  const options = {
    pageNo: 1,
    pageSize: 100000,
  } as HistoryQueryOptions;

  // 根据类型设置查询参数
  if (type === 'account') {
    options.account_id = activeItem.id;
  } else {
    options.fund_id = activeItem.id;
  }

  options.begin_day = dateRange.value[0];
  options.end_day = dateRange.value[1];

  // 调用SDK接口
  const identityType = type === 'account' ? IdentityType.Account.value : IdentityType.Fund.value;
  const response = await recordsRepo.QueryHistoryTradeRecords(identityType, options);

  if (response && response.data) {
    historyRecords.value = response.data.list;
  } else {
    historyRecords.value = [];
  }
};

const asyncDataLoader = async (pageNo: number) => {
  if (!activeItem) return { data: [], total: 0 };

  // 构建查询参数
  const options = {
    pageNo,
    pageSize: PAGE_SIZE,
  } as HistoryQueryOptions;

  // 根据类型设置查询参数
  if (type === 'account') {
    options.account_id = activeItem.id;
  } else {
    options.fund_id = activeItem.id;
  }

  options.begin_day = dateRange.value[0];
  options.end_day = dateRange.value[1];

  // 调用SDK接口
  const identityType = type === 'account' ? IdentityType.Account.value : IdentityType.Fund.value;
  const response = await recordsRepo.QueryHistoryTradeRecords(identityType, options);

  if (response && response.data) {
    return {
      data: response.data.list,
      total: response.data.totalSize,
    };
  } else {
    return {
      data: [],
      total: 0,
    };
  }
};

// 导出数据
const exportData = () => {
  // 实现导出功能
  console.log('导出历史成交数据');
};

onMounted(() => {
  if (activeItem) {
    // fetchHistoryRecords();
  }
});

// 监听activeItem变化
watch(
  () => activeItem,
  newItem => {
    if (newItem) {
      // fetchHistoryRecords();
      tableRef.value?.refreshAsyncData();
    }
  },
  { deep: true },
);

// 监听日期范围变化
watch(dateRange, (newValue, oldValue) => {
  if (newValue[0] !== oldValue[0] || newValue[1] !== oldValue[1]) {
    // fetchHistoryRecords();
    tableRef.value?.refreshAsyncData();
  }
});
</script>

<template>
  <VirtualizedTable
    ref="tableRef"
    :sort="{ key: 'tradeTime', order: TableV2SortOrder.DESC }"
    :columns
    :async-data-loader
    fixed
  >
    <template #left>
      <DateRangePicker v-model="dateRange" />
    </template>
    <template #actions>
      <div class="actions" flex aic>
        <el-button @click="fetchHistoryRecords" size="small" color="var(--g-primary)">
          查询
        </el-button>
        <el-button @click="exportData" size="small">导出</el-button>
      </div>
    </template>
  </VirtualizedTable>
</template>

<style scoped></style>
