<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import { ElMessage } from 'element-plus';
import InstrumentInput from '@/components/common/InstrumentInput.vue';
import TradeDirection from '@/components/NormalTradeView/TradeTabs/TradeDirection.vue';
import type { AccountInfo, InstrumentInfo } from '@/types';
import { TradeDirectionEnum, OrderPriceTypeEnum } from '@/enum';
import { formatNumber } from '@/script';

const { selectedAccount } = defineProps<{
  selectedAccount?: AccountInfo;
}>();

// 交易方向
const direction = ref(TradeDirectionEnum.买入);

// 选中合约
const selectedInstrument = defineModel<InstrumentInfo>('instrument');

// 价格
const price = ref<number>();

// 数量
const volume = ref<number>();

// 价格类型
const priceType = ref(OrderPriceTypeEnum.限价);

// 价格类型选项
const priceTypeOptions = computed(() => [
  { label: '限价', value: OrderPriceTypeEnum.限价 },
  { label: '市价', value: OrderPriceTypeEnum.市价 },
]);

// 预估金额
const estimatedAmount = computed(() => {
  if (!price.value || !volume.value) return 0;
  return price.value * volume.value;
});

// 是否可以下单
const canSubmit = computed(() => {
  return selectedAccount && selectedInstrument.value && price.value && volume.value;
});

// 快速设置数量比例
const setVolumeRatio = (ratio: number) => {
  if (!selectedAccount?.available) {
    ElMessage.warning('请先选择账号');
    return;
  }

  if (!price.value) {
    ElMessage.warning('请先输入价格');
    return;
  }

  const maxVolume = Math.floor(selectedAccount.available / price.value);
  volume.value = Math.floor(maxVolume * ratio);
};

// 提交订单
const submitOrder = async () => {
  if (!canSubmit.value) {
    ElMessage.warning('请完善订单信息');
    return;
  }
};
</script>

<template>
  <div class="trading-panel" h-full flex="~ col">
    <!-- 买卖方向 -->
    <div class="direction-section">
      <TradeDirection v-model="direction" />
    </div>

    <!-- 交易表单 -->
    <div class="form-section" flex-1 p-16 of-auto>
      <!-- 合约选择 -->
      <div class="form-item" mb-16>
        <label class="label">代码</label>
        <InstrumentInput
          v-model="selectedInstrument"
          :asset-type="selectedAccount?.assetType"
          placeholder="请输入合约代码或名称"
        />
      </div>

      <!-- 价格 -->
      <div class="form-item" mb-16>
        <label class="label">限价</label>
        <el-input-number
          v-model="price"
          :precision="2"
          :step="0.01"
          :min="0"
          placeholder="请输入价格"
          w-full
        />
      </div>

      <!-- 数量 -->
      <div class="form-item" mb-4>
        <label class="label">数量</label>
        <el-input-number
          v-model="volume"
          :precision="0"
          :step="100"
          :min="0"
          placeholder="请输入数量"
          w-full
        />
      </div>
      <div class="ratio-buttons" flex gap-8 justify-end mb-10>
        <el-button size="small" @click="setVolumeRatio(0.25)">1/4</el-button>
        <el-button size="small" @click="setVolumeRatio(0.5)">1/2</el-button>
        <el-button size="small" @click="setVolumeRatio(0.75)">3/4</el-button>
        <el-button size="small" @click="setVolumeRatio(1)">全仓</el-button>
      </div>

      <!-- 预估金额 -->
      <div class="form-item" mb-16 v-if="estimatedAmount > 0">
        <label class="label">预估金额</label>
        <div class="estimated-amount" fs-16 fw-600 c-blue-500>
          ¥ {{ formatNumber(estimatedAmount) }}
        </div>
      </div>

      <!-- 投资备注 -->
      <div class="form-item" mb-16>
        <label class="label">投资备注</label>
        <el-input placeholder="请输入备注" />
      </div>
    </div>

    <!-- 下单按钮 -->
    <div class="submit-section">
      <el-button
        :color="direction === TradeDirectionEnum.买入 ? 'var(--g-red)' : 'var(--g-bg-green)'"
        class="submit-button"
        w-full
        :disabled="!canSubmit"
        @click="submitOrder"
      >
        {{ direction === TradeDirectionEnum.买入 ? '买入' : '卖出' }}
      </el-button>
    </div>
  </div>
</template>

<style scoped>
.trading-panel {
  :deep() {
    .el-input-number,
    .el-select {
      width: 100%;
    }
    .el-input__inner {
      text-align: left;
    }
  }

  .action-button {
    .button-inner {
      &.buy {
        background-color: var(--g-red-2);
      }
      &.sell {
        background-color: var(--g-bg-green);
      }
    }
  }

  .form-section {
    .form-item {
      display: flex;
      align-items: center;
      gap: 10px;
      .label {
        display: block;
        color: var(--g-text-color-1);
        & + * {
          flex: 1;
          min-width: 1px;
        }
      }
    }
  }

  .submit-section {
    .submit-button {
      height: 47px;
      font-size: 14px;
      font-weight: 500;
      border-radius: 0;
    }
  }
}
</style>
