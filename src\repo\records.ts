import Utils from '../modules/utils';
import { BaseRepo } from '../modules/base-repo';
import { SocketDataPackage } from '../types/data-package';
import { ServerFunction } from '../config/server-function';
import { ServerEvent } from '../config/server-event';
import { IdentityType } from '../config/trading';
import { GetLogger } from '../global-state';
import { AlterPagedResult, HttpResponseData } from '../types/common';

import {
  PagedResult,
  OrderInfo,
  PositionInfo,
  TradeRecordInfo,
  EquityInfo,
  CashInfo,
  InstructionInfo,
} from '../types/trading';

import {
  QueryOptions,
  SocketQueryOptions,
  HistoryQueryOptions,
  EquityHistoryQueryOptions,
  QueryTodayOrdersOptions,
  QueryTodayPositionsOptions,
} from '../types/query';

const defaultLogger = GetLogger();

export class RecordsRepo extends BaseRepo {
  /**
   * 查询分页数据
   */
  private async QueryByPaging<T>(url: string, options: QueryOptions, pageNo = 1, pageSize = 100) {
    const condtions = this.FormCondition(options, pageNo, pageSize);
    const resp = await this.assist.Post<PagedResult<T>>(url, {}, condtions);
    const { errorCode, errorMsg, data: paged } = resp;

    const paged2 = paged as any;
    const { contents, list } = paged2;
    const matrix = (contents || list || []) as any[][];

    paged2.contents = matrix;
    delete paged2.list;

    if (errorCode === 0 && Array.isArray(matrix) && matrix.length >= 1) {
      const titles = matrix.shift()!;
      const orders = Utils.convertMatrix2Json<T>(titles, matrix);
      resp.data!.contents = orders;
    }

    return resp;
  }

  /**
   * 通过SOCKET方式，发出数据请求，并同时监听回复，形成问答闭合链路
   */
  private async Ask4TodayAll<T>(
    options: SocketQueryOptions,
    fc: ServerFunction,
    serverEvent: ServerEvent,
  ) {
    return new Promise<T[]>((resolve, reject) => {
      const thisObj = this;
      const nextReqId = this.tserver.nextReqId;
      const condtions = this.FormConditionSocket(options);

      /**
       * 超时时则返回空，并记录日志
       */

      const timer = setTimeout(() => {
        thisObj.tserver.unsubscribe(serverEvent, callback);
        const error = 'query by today ask/reply timeout to return';
        defaultLogger.error(error, { nextReqId });
        reject(error);
      }, 1000 * 30);

      function callback(data: SocketDataPackage<PagedResult<T>>) {
        if (data.reqId != nextReqId) {
          defaultLogger.error('Unmatched callback called by server message', { nextReqId, data });
          return;
        }

        clearTimeout(timer);
        thisObj.tserver.unsubscribe(serverEvent, callback);

        const body = data.body! as any;
        const { contents, list } = body;
        const matrix = (contents || list || []) as any[][];
        body.contents = matrix;
        delete body.list;

        if (Array.isArray(matrix) && matrix.length >= 1) {
          const titles = matrix.shift()!;
          const records = Utils.convertMatrix2Json<T>(titles, matrix);
          resolve(records);
        } else {
          resolve([]);
        }
      }

      this.tserver.subscribe(serverEvent, callback);
      this.tserver.send({ fc, reqId: nextReqId, body: condtions });
    });
  }

  /**
   * 构造查询条件
   */
  private FormCondition(options: QueryOptions, pageNo = 1, pageSize = 100) {
    const { identityId, parentOrderId } = options;

    if (Utils.isNone(identityId) && Utils.isNone(parentOrderId)) {
      throw new Error('identityId or parentOrderId is required');
    }

    const condtions = { pageNo, pageSize };

    if (Utils.isNotNone(identityId)) {
      Object.assign(condtions, { identityId });
    } else if (Utils.isNotNone(parentOrderId)) {
      Object.assign(condtions, { parentOrderId });
    }

    return condtions;
  }

  /**
   * 构造查询条件
   */
  private FormConditionSocket(options: SocketQueryOptions) {
    const { identityId, parentOrderId, accountIds } = options;

    if (
      Utils.isNone(identityId) &&
      Utils.isNone(parentOrderId) &&
      (!Array.isArray(accountIds) || accountIds.length == 0)
    ) {
      throw new Error('identityId, parentOrderId or accountIds is required');
    }

    const condtions = {};

    if (Utils.isNotNone(identityId)) {
      Object.assign(condtions, { identityId });
    } else if (Utils.isNotNone(parentOrderId)) {
      Object.assign(condtions, { parentOrderId });
    } else {
      Object.assign(condtions, { accountIds });
    }

    return condtions;
  }

  /**
   * 查询产品、策略、账号今日订单（HTTP通路）
   */
  async QueryTodayOrders(options: QueryOptions, pageNo = 1, pageSize = 100) {
    return await this.QueryByPaging<OrderInfo>('/order/page', options, pageNo, pageSize);
  }

  /**
   * 查询当日订单
   */
  async QueryTodayOrdersNew(options: QueryTodayOrdersOptions, pageNo = 1, pageSize = 30) {
    const resp = await this.assist.Get<any>(
      '../v4/mem/order/page',
      {
        ...options,
        pageNo,
        pageSize,
      },
      {
        headers: this.binaryHeaders,
        responseType: this.binaryResponseType,
      },
    );
    return this.translate<HttpResponseData<PagedResult<OrderInfo>>>(resp);
  }

  /**
   * 查询当日持仓
   */
  async QueryTodayPositionsNew(options: QueryTodayPositionsOptions, pageNo = 1, pageSize = 30) {
    return await this.assist.Get<AlterPagedResult<PositionInfo>>('../v4/mem/position/page', {
      ...options,
      pageNo,
      pageSize,
    });
  }

  /**
   * 查询当日成交
   */
  async QueryTodayTradeRecordsNew(options: QueryTodayOrdersOptions, pageNo = 1, pageSize = 30) {
    return await this.assist.Get<AlterPagedResult<TradeRecordInfo>>('../v4/mem/traderecord/page', {
      ...options,
      pageNo,
      pageSize,
    });
  }

  /**
   * 查询产品、策略、账号今日订单（全量数据，NS/WS通路）
   */
  async QueryTodayOrdersAll(options: SocketQueryOptions) {
    return await this.Ask4TodayAll<OrderInfo>(
      options,
      ServerFunction.RequestTodayOrder,
      ServerEvent.TodayOrderPush,
    );
  }

  /**
   * 查询产品、策略、账号今日持仓（HTTP通路）
   */
  async QueryTodayPositions(options: QueryOptions, pageNo = 1, pageSize = 100) {
    return await this.QueryByPaging<PositionInfo>('/position/page', options, pageNo, pageSize);
  }

  /**
   * 查询产品、策略、账号今日持仓（全量数据，NS/WS通路）
   */
  async QueryTodayPositionsAll(options: SocketQueryOptions) {
    return await this.Ask4TodayAll<PositionInfo>(
      options,
      ServerFunction.RequestTodayPosition,
      ServerEvent.TodayPositionPush,
    );
  }

  /**
   * 查询产品、策略、账号今日成交（HTTP通路）
   */
  async QueryTodayTradeRecords(options: QueryOptions, pageNo = 1, pageSize = 100) {
    return await this.QueryByPaging<TradeRecordInfo>(
      '/traderecord/page',
      options,
      pageNo,
      pageSize,
    );
  }

  /**
   * 查询产品、策略、账号今日成交（全量数据，NS/WS通路）
   */
  async QueryTodayTradeRecordsAll(options: SocketQueryOptions) {
    return await this.Ask4TodayAll<TradeRecordInfo>(
      options,
      ServerFunction.RequestTodayTradeRecord,
      ServerEvent.TodayTradeRecordPush,
    );
  }

  /** 查询当日指令（全量） */
  async QueryTodayInstructionsAll() {
    return await this.assist.Get<AlterPagedResult<InstructionInfo>>('/instruction');
  }

  /**
   * 查询产品、策略、账号今日权益（HTTP通路）
   */
  async QueryTodayEquities(options: QueryOptions, pageNo = 1, pageSize = 100) {
    throw new Error('not implemented');
  }

  /**
   * 查询产品、策略、账号历史订单
   */
  async QueryHistory<T>(itype: number, dtype: string, options: HistoryQueryOptions) {
    const { Fund, Strategy, Account } = IdentityType;
    const typeName =
      itype == Fund.value
        ? 'fund'
        : itype == Strategy.value
        ? 'strategy'
        : itype == Account.value
        ? 'account'
        : itype;
    const url = `history/${typeName}/${dtype}`;
    const resp = await this.assist.Get<AlterPagedResult<T>>(url, options);
    return resp;
  }

  /**
   * 查询产品、策略、账号历史订单
   */
  async QueryHistoryOrders(itype: number, options: HistoryQueryOptions) {
    return await this.QueryHistory<OrderInfo>(itype, 'order', options);
  }

  /**
   * 查询产品、策略、账号历史持仓
   */
  async QueryHistoryPositions(itype: number, options: HistoryQueryOptions) {
    return await this.QueryHistory<PositionInfo>(itype, 'position', options);
  }

  /**
   * 查询产品、策略、账号历史成交
   */
  async QueryHistoryTradeRecords(itype: number, options: HistoryQueryOptions) {
    return await this.QueryHistory<TradeRecordInfo>(itype, 'trade', options);
  }

  /**
   * 查询产品、策略、账号历史权益
   */
  async QueryHistoryEquities(options: EquityHistoryQueryOptions) {
    return await this.assist.Get<EquityInfo[]>('history/balance', options);
  }

  /**
   * 查询出入金记录
   */
  async getIoMoney(identity_id: string | number, trading_day?: string) {
    return await this.assist.Get<CashInfo[]>('/account/cashserial', { identity_id, trading_day });
  }

  /**
   * 添加出入金记录
   */
  async insertIoMoney(
    account_id: string | number,
    params: {
      trading_day: string;
      in_money: number;
      out_money: number;
    },
  ) {
    return await this.assist.Post<void>('/account/cashserial', { account_id, ...params });
  }

  /**
   * 资金比对
   */
  async compareAccountFinance(account_id: string | number, overlap: boolean) {
    return await this.assist.Post<void>('/account/overlap/detail', { account_id, overlap });
  }
}
