<script setup lang="ts">
import { computed, onBeforeUnmount, shallowRef, watch } from 'vue';
import type { InstrumentInfo, StandardTick } from '@/types';
import { formatNumber, getColorClass } from '@/script/formatter';
import { TickService } from '@/api';
import { TickType } from '../../../../xtrade-sdk';

const { selectedInstrument } = defineProps<{
  selectedInstrument?: InstrumentInfo;
}>();

// 最新tick数据
const lastTick = shallowRef<StandardTick>();

// 买卖五档数据
const sellLevels = computed(() => {
  if (!lastTick.value) return [];
  return Array.from({ length: 5 }, (_, i) => ({
    level: 5 - i,
    price: lastTick.value!.askPrice[4 - i],
    volume: lastTick.value!.askVolume[4 - i],
  }));
});

const buyLevels = computed(() => {
  if (!lastTick.value) return [];
  return Array.from({ length: 5 }, (_, i) => ({
    level: i + 1,
    price: lastTick.value!.bidPrice[i],
    volume: lastTick.value!.bidVolume[i],
  }));
});

// 基本行情信息
const marketInfo = computed(() => {
  if (!lastTick.value) return [];

  return [
    { label: '今开', value: lastTick.value.openPrice },
    { label: '昨收', value: lastTick.value.preClosePrice },
    { label: '最高', value: lastTick.value.highPrice },
    { label: '最低', value: lastTick.value.lowPrice },
  ];
});

// 涨跌信息
const priceChange = computed(() => {
  if (!lastTick.value) return { amount: 0, percent: 0 };

  const amount = lastTick.value.lastPrice - lastTick.value.preClosePrice;
  const percent = (amount / lastTick.value.preClosePrice) * 100;

  return { amount, percent };
});

// 模拟账户信息
const accountInfo = computed(() => ({
  accountName: '西南证券账号',
  totalAssets: ********.89,
  marketValue: ********.89,
  availableFunds: 2321234.89,
}));

// 更新时间
const updateTime = computed(() => {
  if (!lastTick.value) return '';
  return lastTick.value.strTime || '11:00:23';
});

// 创建模拟数据
const createMockTick = (): StandardTick => ({
  askPrice: [1220.0, 1220.5, 1221.0, 1221.5, 1222.0],
  askVolume: [52, 52, 52, 52, 52],
  bidPrice: [1220.0, 1219.5, 1219.0, 1218.5, 1218.0],
  bidVolume: [52, 52, 52, 52, 52],
  exchange: 'SH',
  highPrice: 1305.0,
  instrumentID: '688256.SH',
  lastPrice: 1277.34,
  lowPrice: 1172.0,
  upperLimitPrice: 1400.0,
  lowerLimitPrice: 1000.0,
  openPrice: 1220.0,
  position: 0,
  preClosePrice: 1202.0,
  settlePrice: 0,
  strTime: '11:00:23',
  turnover: 1000000,
  updateTime: '11:00:23',
  volume: 10000,
});

// 更新tick数据
const updateTick = (data: StandardTick) => {
  lastTick.value = data;
};

// 监听合约变化，订阅/取消订阅tick数据
watch(
  () => selectedInstrument,
  async (newInstrument, oldInstrument) => {
    // 取消订阅旧合约
    if (oldInstrument) {
      TickService.unsubscribeTick(oldInstrument.instrument, TickType.tick, updateTick);
      lastTick.value = undefined;
    }

    // 订阅新合约或使用模拟数据
    if (newInstrument) {
      try {
        TickService.subscribeTick(newInstrument.instrument, TickType.tick, updateTick);
      } catch (error) {
        // 如果订阅失败，使用模拟数据
        console.warn('Failed to subscribe tick data, using mock data:', error);
        lastTick.value = createMockTick();
      }

      // 如果没有立即收到数据，使用模拟数据
      setTimeout(() => {
        if (!lastTick.value) {
          lastTick.value = createMockTick();
        }
      }, 1000);
    }
  },
  { immediate: true },
);

onBeforeUnmount(() => {
  if (selectedInstrument) {
    TickService.unsubscribeTick(selectedInstrument.instrument, TickType.tick, updateTick);
  }
});
</script>

<template>
  <div class="market-depth" h-full overflow-auto bg="[--g-block-bg-2]" flex="~ col" c-white>
    <!-- 空状态 -->
    <div v-if="!selectedInstrument" class="empty-state" flex-1 flex="~ col" aic jcc c-gray-500>
      <div fs-14>请选择合约查看盘口信息</div>
    </div>
    <!-- 合约信息和最新价 -->
    <div class="header-section" p-16 v-if="selectedInstrument && lastTick">
      <div flex jcsb aic mb-8>
        <div>
          <span class="instrument-name" fs-16 fw-600 c-white mr-8>
            {{ selectedInstrument.instrumentName }}
          </span>
          <!-- <span class="instrument-type" fs-12 bg="[#4a90e2]" c-white px-6 py-2 rounded-4>科</span> -->
        </div>
        <div class="price-info" text-right>
          <div class="last-price" fs-20 fw-600 :class="getColorClass(priceChange.amount)">
            {{ formatNumber(lastTick.lastPrice, { fix: 2 }) }}
            <span class="arrow" fs-12 ml-4>↑</span>
          </div>
        </div>
      </div>

      <div flex jcsb aic>
        <div class="instrument-code" fs-14 c-gray-400>
          {{ selectedInstrument.instrument }}
        </div>
        <div class="change-info" text-right>
          <span class="change-amount" fs-14 :class="getColorClass(priceChange.amount)">
            {{ formatNumber(priceChange.amount, { prefix: true, fix: 2 }) }}
          </span>
          <span class="change-percent" fs-14 :class="getColorClass(priceChange.amount)" ml-8>
            {{ formatNumber(priceChange.percent, { prefix: true, percent: true, fix: 2 }) }}
          </span>
        </div>
      </div>
    </div>

    <!-- 基本行情信息 -->
    <div class="market-info" p-16 v-if="lastTick">
      <div class="info-grid" grid="~ cols-2" gap-y-12 gap-x-16>
        <div v-for="info in marketInfo" :key="info.label" flex jcsb>
          <span class="label" fs-12 c-gray-400>{{ info.label }}</span>
          <span class="value" fs-14 c-white>
            {{ formatNumber(info.value, { fix: 2 }) }}
          </span>
        </div>
      </div>

      <div class="update-time" text-center mt-12 fs-12 c-gray-500>更新时间({{ updateTime }})</div>
    </div>

    <!-- 买卖五档 -->
    <div class="levels-section" flex-1 p-16>
      <!-- 卖档 -->
      <div class="sell-levels" mb-16>
        <div
          v-for="level in sellLevels"
          :key="`sell-${level.level}`"
          class="level-row"
          flex
          jcsb
          aic
          h-28
          px-8
        >
          <span class="level-label" fs-12 c="[#ff6b6b]">卖{{ level.level }}</span>
          <span class="price" fs-14 fw-500 c="[#ff6b6b]">
            {{ formatNumber(level.price, { fix: 2 }) }}
          </span>
          <span class="volume" fs-12 c-gray-400>
            {{ formatNumber(level.volume, { fix: 0 }) }}
          </span>
        </div>
      </div>

      <!-- 买档 -->
      <div class="buy-levels">
        <div
          v-for="level in buyLevels"
          :key="`buy-${level.level}`"
          class="level-row"
          flex
          jcsb
          aic
          h-28
          px-8
        >
          <span class="level-label" fs-12 c="[#51cf66]">买{{ level.level }}</span>
          <span class="price" fs-14 fw-500 c="[#51cf66]">
            {{ formatNumber(level.price, { fix: 2 }) }}
          </span>
          <span class="volume" fs-12 c-gray-400>
            {{ formatNumber(level.volume, { fix: 0 }) }}
          </span>
        </div>
      </div>
    </div>

    <!-- 账户信息 -->
    <div class="account-section" p-16 border-t="1 solid [#333]">
      <div class="account-info" space-y-12>
        <div flex jcsb>
          <span class="label" fs-12 c-gray-400>账号</span>
          <span class="value" fs-14 c-white>{{ accountInfo.accountName }}</span>
        </div>
        <div flex jcsb>
          <span class="label" fs-12 c-gray-400>总资产</span>
          <span class="value" fs-14 c-white>
            {{ formatNumber(accountInfo.totalAssets, { separator: true, fix: 2 }) }}
          </span>
        </div>
        <div flex jcsb>
          <span class="label" fs-12 c-gray-400>总市值</span>
          <span class="value" fs-14 c-white>
            {{ formatNumber(accountInfo.marketValue, { separator: true, fix: 2 }) }}
          </span>
        </div>
        <div flex jcsb>
          <span class="label" fs-12 c-gray-400>可用资金</span>
          <span class="value" fs-14 c-white>
            {{ formatNumber(accountInfo.availableFunds, { separator: true, fix: 2 }) }}
          </span>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.market-depth {
  color: white;
}

.header-section {
  border-bottom: 1px solid #333;
}

.level-row:hover {
  background-color: rgba(255, 255, 255, 0.05);
  border-radius: 4px;
}

.account-section {
  background-color: rgba(0, 0, 0, 0.2);
}

.arrow {
  color: #ff6b6b;
}
</style>
